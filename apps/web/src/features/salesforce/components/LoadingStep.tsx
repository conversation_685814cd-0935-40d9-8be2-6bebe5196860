import {
  SalesforceCloudIcon,
  HubspotIcon,
  SourceIcon,
  SyncingSpinnerIcon,
} from "~/components/icons";

interface LoadingStepProps {
  integrationId: string;
  title: string;
  stage?: "connecting" | "importing";
  logo?: string;
}

export function LoadingStep({
  integrationId,
  title,
  logo,
  stage = "connecting",
}: LoadingStepProps) {
  return (
    <div className="text-center">
      <div className="mb-8 flex items-center justify-center">
        <div>
          <SourceIcon width={80} height={80} className="text-white" />
        </div>

        <div className="mx-2 text-2xl font-bold">
          <SyncingSpinnerIcon
            width={24}
            height={24}
            className="text-gray-400"
          />
        </div>

        <div>
          {integrationId === "salesforce" ? (
            <SalesforceCloudIcon
              width={80}
              height={80}
              variant={stage === "importing" ? "solid" : "dashed"}
            />
          ) : (
            <HubspotIcon
              width={80}
              height={80}
              variant={stage === "importing" ? "solid" : "dashed"}
            />
          )}
        </div>
      </div>

      <p className="mb-4 text-gray-600">{title}</p>

      <p className="text-sm text-gray-500">
        {stage === "connecting"
          ? "Establishing connection..."
          : "Synchronizing data..."}
      </p>
    </div>
  );
}
