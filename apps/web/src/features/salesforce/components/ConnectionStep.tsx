"use client";

import { ArrowRight } from "lucide-react";
import Image from "next/image";

import { useGetSalesforceAuthUrl } from "@pearl/api-hooks";

import { Button } from "~/components/ui/Button";

export function ConnectionStep() {
  const { data, isLoading, isError, error } = useGetSalesforceAuthUrl();

  function handleConnectToSalesforce() {
    if (data?.authUrl) {
      window.location.href = data.authUrl;
    }
  }

  return (
    <div className="mx-auto flex h-[400px] max-w-md flex-col items-center justify-between">
      <div className="w-full text-center">
        <div className="mb-4 flex justify-center">
          <div className="flex h-[90px] w-[90px] items-center justify-center">
            <Image
              src="/integrations/salesforce.png"
              alt="Salesforce Logo"
              width={72}
              height={72}
              className="max-h-full max-w-full object-contain"
            />
          </div>
        </div>
        <h1 className="mb-8 text-2xl font-bold">Connect to Salesforce</h1>
      </div>

      <div className="mb-auto w-full max-w-md flex-grow overflow-auto text-center">
        <p className="mb-4 text-gray-600">
          Connecting to Salesforce allows Pearl to access your pipeline,
          understand your accounts, and surface relevant insights—so you don't
          have to repeat yourself or dig through scattered notes.
        </p>
        <p className="mb-8 text-sm text-gray-500">
          Your data stays secure and only you can see it.
        </p>
      </div>

      <div className="h-[60px] w-full max-w-md">
        <Button
          onClick={handleConnectToSalesforce}
          className="w-full"
          size="lg"
          disabled={isLoading || !data?.authUrl}
        >
          {isLoading ? "Loading..." : "Connect with Salesforce"}
          {!isLoading && <ArrowRight size={16} className="ml-2" />}
        </Button>

        {isError && (
          <p className="mt-2 text-sm text-red-500">
            {error instanceof Error
              ? error.message
              : "Failed to get authorization URL"}
          </p>
        )}
      </div>
    </div>
  );
}
